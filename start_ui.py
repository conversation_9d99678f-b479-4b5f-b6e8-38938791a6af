#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动 Streamlit UI 界面
使用 uv run 运行: uv run start_ui.py
"""

import subprocess
import sys
import os
from pathlib import Path

def main():
    """启动 Streamlit UI"""
    
    print("🚀 启动 Augment Code 自动注册工具 - UI 界面")
    print("=" * 60)
    
    # 检查是否安装了 streamlit
    try:
        import streamlit
        print("✅ Streamlit 已安装")
    except ImportError:
        print("❌ Streamlit 未安装，正在安装...")
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", "streamlit", "streamlit-option-menu", "streamlit-ace"], check=True)
            print("✅ Streamlit 安装完成")
        except subprocess.CalledProcessError:
            print("❌ Streamlit 安装失败，请手动安装:")
            print("   uv add streamlit streamlit-option-menu streamlit-ace")
            return
    
    # 检查是否安装了 playwright
    try:
        import playwright
        print("✅ Playwright 已安装")
    except ImportError:
        print("❌ Playwright 未安装，正在安装...")
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", "playwright"], check=True)
            subprocess.run([sys.executable, "-m", "playwright", "install", "chromium"], check=True)
            print("✅ Playwright 安装完成")
        except subprocess.CalledProcessError:
            print("❌ Playwright 安装失败，请手动安装:")
            print("   uv add playwright")
            print("   uv run playwright install chromium")
            return
    
    # 启动 Streamlit
    ui_file = Path(__file__).parent / "ui_app.py"
    
    if not ui_file.exists():
        print(f"❌ UI 文件不存在: {ui_file}")
        return
    
    print(f"🌐 启动 UI 界面: {ui_file}")
    print("📱 浏览器将自动打开，如果没有请手动访问显示的地址")
    print("🛑 按 Ctrl+C 停止服务")
    print("=" * 60)
    
    try:
        # 使用 streamlit run 启动
        cmd = [
            sys.executable, "-m", "streamlit", "run", str(ui_file),
            "--server.port", "8501",
            "--server.address", "localhost",
            "--browser.gatherUsageStats", "false"
        ]
        
        subprocess.run(cmd, check=True)
        
    except KeyboardInterrupt:
        print("\n👋 UI 界面已关闭")
    except subprocess.CalledProcessError as e:
        print(f"❌ 启动失败: {e}")
        print("请尝试手动运行:")
        print(f"   uv run streamlit run {ui_file}")
    except Exception as e:
        print(f"❌ 未知错误: {e}")

if __name__ == "__main__":
    main()
