// ==UserScript==
// @name         AugmentCode自动注册
// @namespace    http://tampermonkey.net/
// @version      0.2.0
// @description  自动完成AugmentCode的注册流程，支持文件上传
// <AUTHOR> name
// @match        https://*.augmentcode.com/*
// @icon         https://www.google.com/s2/favicons?sz=64&domain=augmentcode.com
// @grant        GM_xmlhttpRequest
// @grant        GM_setValue
// @grant        GM_getValue
// @connect      184772.xyz
// ==/UserScript==

(function() {
    'use strict';

    // 邮箱服务类型
    const EMAIL_SERVICES = {
        GOMAIL: 'gomail',
        STANDARD: 'standard'
    };

    /**
     * GoMail 服务配置
     */
    const GOMAIL_CONFIG = {
        apiBase: 'https://184772.xyz',
        token: GM_getValue('gomail_token', 'gm_your_token_here'),
        domain: '184772.xyz'
    };

    // 当前选择的邮箱服务
    let currentEmailService = GM_getValue('email_service', EMAIL_SERVICES.GOMAIL);

    // 保存的上传文件 (持久化存储)
    let savedUploadFile = null;

    // 从存储中恢复文件数据
    function restoreSavedFile() {
        const savedFileData = GM_getValue('saved_upload_file', null);
        if (savedFileData) {
            try {
                const fileInfo = JSON.parse(savedFileData);
                // 创建一个虚拟文件对象用于显示信息
                savedUploadFile = {
                    name: fileInfo.name,
                    size: fileInfo.size,
                    type: fileInfo.type,
                    content: fileInfo.content,
                    isRestored: true // 标记这是从存储恢复的文件
                };
            } catch (error) {
                GM_setValue('saved_upload_file', null);
            }
        }
    }

    // 保存文件到存储
    function saveFileToStorage(file) {
        return new Promise((resolve) => {
            const reader = new FileReader();
            reader.onload = function(e) {
                const fileData = {
                    name: file.name,
                    size: file.size,
                    type: file.type,
                    content: e.target.result, // base64 编码的文件内容
                    savedAt: Date.now()
                };
                GM_setValue('saved_upload_file', JSON.stringify(fileData));
                resolve();
            };
            reader.readAsDataURL(file);
        });
    }

    // 从存储的数据创建真实的 File 对象
    function createFileFromSaved(savedFileData) {
        if (!savedFileData || !savedFileData.content) return null;

        try {
            // 将 base64 转换回 Blob
            const response = fetch(savedFileData.content);
            return response.then(res => res.blob()).then(blob => {
                return new File([blob], savedFileData.name, { type: savedFileData.type });
            });
        } catch (error) {
            return null;
        }
    }

    // 初始化时恢复保存的文件
    restoreSavedFile();

    // 获取服务显示名称
    function getServiceDisplayName(service) {
        switch(service) {
            case EMAIL_SERVICES.GOMAIL:
                return 'GoMail';
            case EMAIL_SERVICES.STANDARD:
                return '标准邮箱';
            default:
                return '未知服务';
        }
    }

    const FIRST_NAMES = ["alex", "emily", "jason", "olivia", "ryan", "sophia", "thomas", "isabella", "william", "mia", "james", "ava", "noah", "charlotte", "ethan", "amelia", "jacob", "evelyn", "mason", "abigail"];
    const LAST_NAMES = ["taylor", "anderson", "thompson", "jackson", "white", "harris", "martin", "thomas", "lewis", "clark", "lee", "walker", "hall", "young", "allen", "king", "wright", "scott", "green", "adams"];


    // 颜色配置
    const COLORS = {
        primary: '#3498db',
        secondary: '#2ecc71',
        danger: '#e74c3c',
        warning: '#f39c12',
        info: '#34495e',
        light: '#ecf0f1',
        dark: '#2c3e50',
        background: 'rgba(30, 30, 30, 0.95)'
    };

    // 日志UI配置
    const LOG_UI_CONFIG = {
        position: {
            bottom: 40,
            left: 20
        },
        dimensions: {
            width: 320,
            maxHeight: 450 
        }
    };


    function createLogUI() {
        const logContainer = document.createElement('div');
        logContainer.id = "auto-register-log";
        logContainer.style.cssText = `
            position: fixed;
            bottom: ${LOG_UI_CONFIG.position.bottom}px;
            left: ${LOG_UI_CONFIG.position.left}px;
            width: ${LOG_UI_CONFIG.dimensions.width}px;
            max-height: ${LOG_UI_CONFIG.dimensions.maxHeight}px;
            background: ${COLORS.background};
            border-radius: 10px;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.25);
            z-index: 10000;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        `;

        logContainer.innerHTML = `
            <div style="
                padding: 14px 16px;
                background: ${COLORS.primary};
                color: white;
                font-weight: 600;
                display: flex;
                justify-content: space-between;
                align-items: center;
                border-bottom: 2px solid ${COLORS.secondary};
            ">
                <span>自动注册助手</span>
                <div>
                    <button id="settings-btn" style="
                        background: ${COLORS.warning};
                        border: none;
                        color: white;
                        cursor: pointer;
                        font-size: 13px;
                        padding: 6px 12px;
                        border-radius: 4px;
                        margin-right: 8px;
                        transition: all 0.2s ease;
                    ">设置</button>
                    <button id="auto-register-btn" style="
                        background: ${COLORS.secondary};
                        border: none;
                        color: white;
                        cursor: pointer;
                        font-size: 13px;
                        padding: 6px 12px;
                        border-radius: 4px;
                        margin-right: 8px;
                        display: none;
                        transition: all 0.2s ease;
                    ">开始注册</button>
                    <button id="clear-log" style="
                        background: transparent;
                        border: 1px solid rgba(255, 255, 255, 0.7);
                        color: white;
                        cursor: pointer;
                        font-size: 13px;
                        padding: 6px 12px;
                        border-radius: 4px;
                        transition: all 0.2s ease;
                    ">清除</button>
                    <button id="minimize-log" style="
                        background: transparent;
                        border: none;
                        color: white;
                        cursor: pointer;
                        font-size: 16px;
                        padding: 6px 12px;
                        margin-left: 8px;
                        transition: all 0.2s ease;
                    ">_</button>
                </div>
            </div>
            <div style="
                padding: 8px 16px;
                background: ${COLORS.dark};
                border-bottom: 1px solid ${COLORS.info};
                font-size: 12px;
                color: ${COLORS.light};
                display: flex;
                align-items: center;
                gap: 8px;
            ">
                <span style="color: ${COLORS.secondary};">📢</span>
                <span>操作控制台 - 当前邮箱服务: <span id="current-service">${getServiceDisplayName(currentEmailService)}</span></span>
            </div>
            <div id="settings-panel" style="
                padding: 16px;
                background: ${COLORS.dark};
                border-bottom: 1px solid ${COLORS.info};
                display: none;
                max-height: 300px;
                overflow-y: auto;
            ">
                <div style="margin-bottom: 12px;">
                    <label style="color: ${COLORS.light}; font-size: 12px; display: block; margin-bottom: 4px;">邮箱服务:</label>
                    <select id="email-service-select" style="
                        width: 100%;
                        padding: 6px;
                        border-radius: 4px;
                        border: 1px solid ${COLORS.info};
                        background: ${COLORS.background};
                        color: ${COLORS.light};
                        font-size: 12px;
                    ">
                        <option value="${EMAIL_SERVICES.GOMAIL}">GoMail (184772.xyz)</option>
                        <option value="${EMAIL_SERVICES.STANDARD}">标准邮箱</option>
                    </select>
                </div>
                <div id="gomail-token-section" style="margin-bottom: 12px; display: none;">
                    <label style="color: ${COLORS.light}; font-size: 12px; display: block; margin-bottom: 4px;">GoMail Token:</label>
                    <div style="display: flex; gap: 8px;">
                        <input type="text" id="gomail-token-input" placeholder="输入您的 GoMail API Token" style="
                            flex: 1;
                            padding: 6px;
                            border-radius: 4px;
                            border: 1px solid ${COLORS.info};
                            background: ${COLORS.background};
                            color: ${COLORS.light};
                            font-size: 12px;
                            box-sizing: border-box;
                        ">
                        <button id="test-token-btn" style="
                            background: ${COLORS.info};
                            border: none;
                            color: white;
                            cursor: pointer;
                            font-size: 12px;
                            padding: 6px 12px;
                            border-radius: 4px;
                            transition: all 0.2s ease;
                        ">测试</button>
                    </div>
                </div>
                <div style="margin-bottom: 12px;">
                    <label style="color: ${COLORS.light}; font-size: 12px; display: block; margin-bottom: 4px;">文件上传 (promotions/cursor):</label>
                    <input type="file" id="upload-file-input" style="
                        width: 100%;
                        padding: 6px;
                        border-radius: 4px;
                        border: 1px solid ${COLORS.info};
                        background: ${COLORS.background};
                        color: ${COLORS.light};
                        font-size: 12px;
                        box-sizing: border-box;
                        margin-bottom: 8px;
                    ">
                    <div id="saved-file-info" style="
                        background: rgba(46, 204, 113, 0.2);
                        border: 1px solid ${COLORS.secondary};
                        border-radius: 4px;
                        padding: 8px;
                        margin-bottom: 8px;
                        font-size: 12px;
                        color: ${COLORS.secondary};
                        display: none;
                    ">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <span id="saved-file-name">未选择文件</span>
                            <button id="clear-saved-file" style="
                                background: transparent;
                                border: 1px solid ${COLORS.secondary};
                                color: ${COLORS.secondary};
                                cursor: pointer;
                                font-size: 10px;
                                padding: 2px 6px;
                                border-radius: 3px;
                            ">清除</button>
                        </div>
                    </div>
                    <button id="upload-file-btn" style="
                        width: 100%;
                        background: ${COLORS.warning};
                        border: none;
                        color: white;
                        cursor: pointer;
                        font-size: 12px;
                        padding: 8px 16px;
                        border-radius: 4px;
                        transition: all 0.2s ease;
                    ">上传文件</button>
                </div>
                <div style="margin-bottom: 12px;">
                    <label style="display: flex; align-items: center; cursor: pointer; color: ${COLORS.light}; font-size: 12px;">
                        <input type="checkbox" id="auto-upload-invoice" style="margin-right: 8px;">
                        <span>自动上传账单 (promotions/cursor页面)</span>
                    </label>
                    <small style="color: ${COLORS.warning}; font-size: 11px; margin-left: 20px;">
                        开启后将在页面加载时自动上传PDF账单并点击Upload Invoice按钮
                    </small>
                </div>
                <button id="save-settings" style="
                    background: ${COLORS.secondary};
                    border: none;
                    color: white;
                    cursor: pointer;
                    font-size: 12px;
                    padding: 8px 16px;
                    border-radius: 4px;
                    width: 100%;
                    transition: all 0.2s ease;
                ">保存设置</button>
            </div>
            <div id="log-content" style="
                padding: 16px;
                overflow-y: auto;
                max-height: calc(${LOG_UI_CONFIG.dimensions.maxHeight}px - 200px);
                font-size: 14px;
                color: ${COLORS.light};
                line-height: 1.5;
            "></div>
        `;

        document.body.appendChild(logContainer);

        // 初始化设置
        const emailServiceSelect = document.getElementById('email-service-select');
        const gomailTokenInput = document.getElementById('gomail-token-input');
        const gomailTokenSection = document.getElementById('gomail-token-section');
        const settingsPanel = document.getElementById('settings-panel');
        const settingsBtn = document.getElementById('settings-btn');
        const saveSettingsBtn = document.getElementById('save-settings');
        const testTokenBtn = document.getElementById('test-token-btn');
        const uploadFileInput = document.getElementById('upload-file-input');
        const uploadFileBtn = document.getElementById('upload-file-btn');

        const clearSavedFileBtn = document.getElementById('clear-saved-file');
        const autoUploadInvoiceCheckbox = document.getElementById('auto-upload-invoice');

        // 设置初始值
        emailServiceSelect.value = currentEmailService;
        gomailTokenInput.value = GOMAIL_CONFIG.token;
        autoUploadInvoiceCheckbox.checked = GM_getValue('auto_upload_invoice', true);

        // 根据当前服务显示/隐藏token输入框
        function updateTokenVisibility() {
            if (emailServiceSelect.value === EMAIL_SERVICES.GOMAIL) {
                gomailTokenSection.style.display = 'block';
            } else {
                gomailTokenSection.style.display = 'none';
            }
        }
        updateTokenVisibility();

        // 初始化保存文件显示
        updateSavedFileDisplay();

        // 设置按钮事件
        settingsBtn.addEventListener('click', () => {
            const isVisible = settingsPanel.style.display !== 'none';
            settingsPanel.style.display = isVisible ? 'none' : 'block';
        });

        // 邮箱服务选择变化事件
        emailServiceSelect.addEventListener('change', updateTokenVisibility);

        // 测试 Token 事件
        testTokenBtn.addEventListener('click', async () => {
            const token = gomailTokenInput.value.trim();
            if (!token || token === 'gm_your_token_here') {
                logger.log('请先输入 GoMail Token', 'warning');
                return;
            }

            const originalText = testTokenBtn.textContent;
            testTokenBtn.disabled = true;
            testTokenBtn.textContent = '测试中...';

            try {
                // 临时更新 token 进行验证
                const oldToken = GOMAIL_CONFIG.token;
                GOMAIL_CONFIG.token = token;

                const validation = await validateGomailToken();
                GOMAIL_CONFIG.token = oldToken; // 恢复原 token

                if (validation.valid) {
                    logger.log(`Token 有效！剩余使用次数: ${validation.info.remainingUsage}/${validation.info.usageLimit}`, 'success');
                } else {
                    logger.log('Token 无效: ' + validation.reason, 'error');
                }
            } catch (error) {
                logger.log('Token 验证出错: ' + error.message, 'error');
            } finally {
                testTokenBtn.disabled = false;
                testTokenBtn.textContent = originalText;
            }
        });

        // 文件选择事件 - 保存选择的文件到持久化存储
        uploadFileInput.addEventListener('change', async (e) => {
            if (e.target.files.length > 0) {
                const file = e.target.files[0];


                try {
                    await saveFileToStorage(file);
                    savedUploadFile = file;
                    updateSavedFileDisplay();
                    logger.log(`文件已永久保存: ${file.name} (${(file.size / 1024).toFixed(2)} KB)`, 'success');
                } catch (error) {
                    logger.log(`保存文件失败: ${error.message}`, 'error');
                }
            }
        });

        // 清除保存的文件
        clearSavedFileBtn.addEventListener('click', () => {
            savedUploadFile = null;
            uploadFileInput.value = '';
            GM_setValue('saved_upload_file', null);
            updateSavedFileDisplay();

        });

        // 文件上传事件 - 使用保存的文件或当前选择的文件
        uploadFileBtn.addEventListener('click', async () => {
            let fileToUpload = null;

            // 优先使用保存的文件
            if (savedUploadFile) {
                if (savedUploadFile.isRestored) {
                    // 如果是从存储恢复的文件，需要重新创建 File 对象
                    try {
                        fileToUpload = await createFileFromSaved(savedUploadFile);
                        if (!fileToUpload) {
                            logger.log('恢复保存的文件失败，请重新选择文件', 'error');
                            return;
                        }
                    } catch (error) {
                        logger.log('恢复保存的文件失败: ' + error.message, 'error');
                        return;
                    }
                } else {
                    fileToUpload = savedUploadFile;
                }
            }

            // 如果没有保存的文件，尝试使用当前选择的文件
            if (!fileToUpload && uploadFileInput.files.length > 0) {
                fileToUpload = uploadFileInput.files[0];
                // 自动保存新选择的文件
                try {
                    await saveFileToStorage(fileToUpload);
                    savedUploadFile = fileToUpload;
                    updateSavedFileDisplay();
                    logger.log(`文件已自动保存: ${fileToUpload.name}`, 'success');
                } catch (error) {
                    logger.log(`自动保存文件失败: ${error.message}`, 'warning');
                }
            }

            if (!fileToUpload) {
                logger.log('请先选择要上传的文件', 'warning');
                return;
            }

            uploadFile(fileToUpload);
        });

        // 保存设置事件
        saveSettingsBtn.addEventListener('click', async () => {
            const originalText = saveSettingsBtn.textContent;
            saveSettingsBtn.disabled = true;
            saveSettingsBtn.textContent = '验证中...';

            try {
                currentEmailService = emailServiceSelect.value;

                if (currentEmailService === EMAIL_SERVICES.GOMAIL) {
                    const newToken = gomailTokenInput.value.trim();
                    if (!newToken || newToken === 'gm_your_token_here') {
                        logger.log('请输入有效的 GoMail Token', 'error');
                        return;
                    }

                    // 临时更新 token 进行验证
                    const oldToken = GOMAIL_CONFIG.token;
                    GOMAIL_CONFIG.token = newToken;

                    try {
                        const validation = await validateGomailToken();
                        if (validation.valid) {
                            GM_setValue('gomail_token', newToken);
                            logger.log(`Token 验证成功！剩余使用次数: ${validation.info.remainingUsage}`, 'success');
                        } else {
                            GOMAIL_CONFIG.token = oldToken; // 恢复旧 token
                            logger.log('Token 验证失败: ' + validation.reason, 'error');
                            return;
                        }
                    } catch (error) {
                        GOMAIL_CONFIG.token = oldToken; // 恢复旧 token
                        logger.log('Token 验证出错: ' + error.message, 'error');
                        return;
                    }
                }

                GM_setValue('email_service', currentEmailService);

                // 保存自动上传设置
                const autoUploadEnabled = autoUploadInvoiceCheckbox.checked;
                GM_setValue('auto_upload_invoice', autoUploadEnabled);

                // 更新显示
                document.getElementById('current-service').textContent = getServiceDisplayName(currentEmailService);
                settingsPanel.style.display = 'none';

                logger.log('设置已保存' + (autoUploadEnabled ? '，自动上传已启用' : ''), 'success');
            } finally {
                saveSettingsBtn.disabled = false;
                saveSettingsBtn.textContent = originalText;
            }
        });

        // 最小化功能
        let isMinimized = false;
        const logContent = document.getElementById('log-content');
        const minimizeBtn = document.getElementById('minimize-log');

        minimizeBtn.addEventListener('click', () => {
            isMinimized = !isMinimized;
            logContent.style.display = isMinimized ? 'none' : 'block';
            settingsPanel.style.display = 'none'; // 最小化时隐藏设置面板
            minimizeBtn.textContent = isMinimized ? '▢' : '_';
        });

        // 清除日志功能
        const clearBtn = document.getElementById('clear-log');
        clearBtn.addEventListener('click', () => {
            logContent.innerHTML = '';

        });

        // 注册按钮悬停效果
        const registerBtn = document.getElementById('auto-register-btn');
        if (registerBtn) {
            registerBtn.addEventListener('mouseenter', () => {
                registerBtn.style.transform = 'scale(1.05)';
            });
            registerBtn.addEventListener('mouseleave', () => {
                registerBtn.style.transform = 'scale(1)';
            });
        }

        return {
            log: function(message, type = 'info') {
                const logEntry = document.createElement('div');
                logEntry.style.marginBottom = '10px';
                logEntry.style.padding = '12px';
                logEntry.style.borderRadius = '6px';
                logEntry.style.wordBreak = 'break-all';
                logEntry.style.transition = 'all 0.3s ease';

                let bgColor, textColor;

                switch(type) {
                    case 'success':
                        bgColor = 'rgba(46, 204, 113, 0.2)';
                        textColor = COLORS.secondary;
                        break;
                    case 'error':
                        bgColor = 'rgba(231, 76, 60, 0.2)';
                        textColor = COLORS.danger;
                        break;
                    case 'warning':
                        bgColor = 'rgba(243, 156, 17, 0.2)';
                        textColor = COLORS.warning;
                        break;
                    default:
                        bgColor = 'rgba(255, 255, 255, 0.05)';
                        textColor = COLORS.light;
                }

                logEntry.style.backgroundColor = bgColor;
                logEntry.style.color = textColor;

                const time = new Date().toLocaleTimeString([], { hour: '2-digit', minute:'2-digit', second:'2-digit' });
                logEntry.textContent = `[${time}] ${message}`;
                logContent.appendChild(logEntry);
                logContent.scrollTop = logContent.scrollHeight;
            },
            showRegisterButton: function() {
                const registerBtn = document.getElementById('auto-register-btn');
                if (registerBtn) {
                    this.log('找到注册按钮，正在显示...');
                    registerBtn.style.display = 'inline-block';
                    return registerBtn;
                } else {
                    this.log('未找到注册按钮元素', 'error');
                    return null;
                }
            }
        };
    }

    // 创建全局日志对象
    const logger = createLogUI();

    // 验证 GoMail Token 是否有效
    async function validateGomailToken() {
        return new Promise((resolve, reject) => {
            GM_xmlhttpRequest({
                method: "GET",
                url: `${GOMAIL_CONFIG.apiBase}/api/external/mailbox`,
                headers: {
                    'Authorization': `Bearer ${GOMAIL_CONFIG.token}`
                },
                onload: function(response) {
                    try {
                        const data = JSON.parse(response.responseText);
                        if (data.success && data.data) {
                            const tokenInfo = data.data;
                            if (tokenInfo.isActive && tokenInfo.usable) {
                                resolve({
                                    valid: true,
                                    info: tokenInfo
                                });
                            } else {
                                resolve({
                                    valid: false,
                                    reason: tokenInfo.reason || 'Token 不可用'
                                });
                            }
                        } else {
                            resolve({
                                valid: false,
                                reason: data.message || 'Token 验证失败'
                            });
                        }
                    } catch (error) {
                        reject(new Error('解析 Token 验证响应失败: ' + error.message));
                    }
                },
                onerror: function(error) {
                    reject(new Error('Token 验证网络请求失败: ' + error));
                }
            });
        });
    }

    // 生成随机邮箱前缀
    function generateRandomPrefix() {
        const firstName = FIRST_NAMES[Math.floor(Math.random() * FIRST_NAMES.length)];
        const lastName = LAST_NAMES[Math.floor(Math.random() * LAST_NAMES.length)];
        const timestamp = Date.now().toString(36); // 转换为36进制以缩短长度
        const randomNum = Math.floor(Math.random() * 1000).toString().padStart(3, '0'); // 生成3位随机数
        return `${firstName}${lastName}${timestamp}${randomNum}`;
    }

    // GoMail API 函数
    async function createGomailMailbox() {
        return new Promise((resolve, reject) => {
            const prefix = generateRandomPrefix();

            GM_xmlhttpRequest({
                method: "POST",
                url: `${GOMAIL_CONFIG.apiBase}/api/external/mailbox`,
                headers: {
                    'Authorization': `Bearer ${GOMAIL_CONFIG.token}`,
                    'Content-Type': 'application/json'
                },
                data: JSON.stringify({
                    prefix: prefix,
                    domain: '184772.xyz'
                }),
                onload: function(response) {
                    try {
                        const data = JSON.parse(response.responseText);
                        if (data.success && data.data && data.data.email) {
                            resolve(data.data.email);
                        } else {
                            reject(new Error('创建邮箱失败: ' + (data.message || '未知错误')));
                        }
                    } catch (error) {
                        reject(new Error('解析响应失败: ' + error.message));
                    }
                },
                onerror: function(error) {
                    reject(new Error('网络请求失败: ' + error));
                }
            });
        });
    }

    async function getGomailEmails(email) {
        return new Promise((resolve, reject) => {
            GM_xmlhttpRequest({
                method: "GET",
                url: `${GOMAIL_CONFIG.apiBase}/api/external/emails/${email}`,
                headers: {
                    'Authorization': `Bearer ${GOMAIL_CONFIG.token}`
                },
                onload: function(response) {
                    try {
                        const data = JSON.parse(response.responseText);
                        if (data.success && data.data && data.data.emails) {
                            resolve(data.data.emails);
                        } else {
                            resolve([]);
                        }
                    } catch (error) {
                        reject(new Error('解析邮件列表失败: ' + error.message));
                    }
                },
                onerror: function(error) {
                    reject(new Error('获取邮件列表失败: ' + error));
                }
            });
        });
    }

    async function getGomailEmailDetail(emailId) {
        return new Promise((resolve, reject) => {
            GM_xmlhttpRequest({
                method: "GET",
                url: `${GOMAIL_CONFIG.apiBase}/api/external/email/${emailId}`,
                headers: {
                    'Authorization': `Bearer ${GOMAIL_CONFIG.token}`
                },
                onload: function(response) {
                    try {
                        const data = JSON.parse(response.responseText);
                        if (data.success && data.data) {
                            resolve(data.data);
                        } else {
                            reject(new Error('获取邮件详情失败: ' + (data.message || '未知错误')));
                        }
                    } catch (error) {
                        reject(new Error('解析邮件详情失败: ' + error.message));
                    }
                },
                onerror: function(error) {
                    reject(new Error('网络请求失败: ' + error));
                }
            });
        });
    }

    // 更新保存文件的显示
    function updateSavedFileDisplay() {
        const savedFileInfo = document.getElementById('saved-file-info');
        const savedFileName = document.getElementById('saved-file-name');

        if (savedUploadFile) {
            const statusText = savedUploadFile.isRestored ? ' [已保存]' : ' [当前选择]';
            savedFileName.textContent = `${savedUploadFile.name} (${(savedUploadFile.size / 1024).toFixed(2)} KB)${statusText}`;
            savedFileInfo.style.display = 'block';
        } else {
            savedFileInfo.style.display = 'none';
        }
    }

    // 文件上传相关函数
    function uploadFile(file) {
        logger.log(`准备上传文件: ${file.name} (${(file.size / 1024).toFixed(2)} KB)`);

        // 查找拖拽区域或文件输入框
        const dropZone = document.querySelector('.drag-drop-zone') ||
                        document.querySelector('[data-testid="file-upload-zone"]') ||
                        document.querySelector('.upload-area') ||
                        document.querySelector('input[type="file"]');

        if (!dropZone) {
            logger.log('未找到文件上传区域', 'error');
            return;
        }

        try {
            if (dropZone.tagName === 'INPUT' && dropZone.type === 'file') {
                // 如果是文件输入框，直接设置文件
                const dataTransfer = new DataTransfer();
                dataTransfer.items.add(file);
                dropZone.files = dataTransfer.files;
                dropZone.dispatchEvent(new Event('change', { bubbles: true }));
                logger.log('文件已设置到输入框', 'success');
            } else {
                // 如果是拖拽区域，模拟拖拽事件
                const dataTransfer = new DataTransfer();
                dataTransfer.items.add(file);

                dropZone.dispatchEvent(new DragEvent('dragover', { bubbles: true, dataTransfer }));
                dropZone.dispatchEvent(new DragEvent('drop', { bubbles: true, dataTransfer }));
                logger.log('文件已模拟拖拽上传', 'success');
            }
        } catch (error) {
            logger.log('文件上传失败: ' + error.message, 'error');
        }
    }



    // 等待元素出现的辅助函数
    async function waitForUploadElement(timeout = 10000) {
        const selectors = [
            '.drag-drop-zone',
            '[data-testid="file-upload-zone"]',
            '.upload-area',
            'input[type="file"]'
        ];

        const startTime = Date.now();
        while (Date.now() - startTime < timeout) {
            for (const selector of selectors) {
                const element = document.querySelector(selector);
                if (element) {
                    return element;
                }
            }
            await new Promise(resolve => setTimeout(resolve, 100));
        }
        return null;
    }

    // 等待 Upload Invoice 按钮可点击
    async function waitForUploadInvoiceButton(timeout = 30000) {


        const startTime = Date.now();
        while (Date.now() - startTime < timeout) {
            // 多种方式查找按钮
            let button = null;

            // 方式1: 精确匹配您提供的选择器
            button = document.querySelector('button.rt-reset.rt-BaseButton.rt-r-size-2.rt-variant-solid.rt-Button');

            // 方式2: 通过文本内容查找
            if (!button) {
                const buttons = Array.from(document.querySelectorAll('button'));
                button = buttons.find(btn =>
                    btn.textContent.trim() === 'Upload Invoice' ||
                    btn.textContent.includes('Upload Invoice')
                );
            }

            // 方式3: 通过部分类名查找
            if (!button) {
                button = document.querySelector('button.rt-Button') ||
                        document.querySelector('button[class*="rt-Button"]') ||
                        document.querySelector('button[class*="Button"]');
            }

            // 检查按钮是否可点击
            if (button && !button.disabled && button.offsetParent !== null) {
                // 额外检查按钮文本是否包含 Upload 相关内容
                const buttonText = button.textContent.trim();
                if (buttonText.includes('Upload') || buttonText.includes('upload')) {
                    return button;
                }
            }

            // 每0.5秒检查一次
            await new Promise(resolve => setTimeout(resolve, 500));


        }

        return null;
    }

    // 检查是否需要上传账单（查找上传相关元素）
    function checkIfNeedUpload() {
        // 查找上传相关的元素
        const uploadElements = [
            document.querySelector('.drag-drop-zone'),
            document.querySelector('[data-testid="file-upload-zone"]'),
            document.querySelector('.upload-area'),
            document.querySelector('input[type="file"]'),
            ...Array.from(document.querySelectorAll('*')).filter(el =>
                el.textContent && (
                    el.textContent.includes('Upload Invoice') ||
                    el.textContent.includes('Drag and drop') ||
                    el.textContent.includes('Choose file')
                )
            )
        ].filter(Boolean);

        return uploadElements.length > 0;
    }

    // 检查是否已经完成上传（有可见可点击的 Return to Home 按钮）
    function checkIfAlreadyCompleted() {
        const returnHomeButton = Array.from(document.querySelectorAll('button')).find(btn =>
            btn.textContent.includes('Return to Home')
        );

        // 检查按钮是否真的可见可点击
        if (returnHomeButton && !returnHomeButton.disabled && returnHomeButton.offsetParent !== null) {
            // 进一步检查按钮是否真的可见
            const rect = returnHomeButton.getBoundingClientRect();
            return rect.width > 0 && rect.height > 0;
        }

        return false;
    }

    // 等待并点击 Return to Home 按钮
    async function waitAndClickReturnHome(timeout = 15000) {

        const startTime = Date.now();
        while (Date.now() - startTime < timeout) {
            const returnHomeButton = Array.from(document.querySelectorAll('button')).find(btn =>
                btn.textContent.includes('Return to Home')
            );

            if (returnHomeButton && !returnHomeButton.disabled && returnHomeButton.offsetParent !== null) {
                // 进一步检查按钮是否真的可见
                const rect = returnHomeButton.getBoundingClientRect();
                const isVisible = rect.width > 0 && rect.height > 0;

                if (isVisible) {
                    // 设置标记，表示刚刚完成了账单上传
                    GM_setValue('just_completed_upload', Date.now());

                    returnHomeButton.click();
                    logger.log('✅ 已点击 Return to Home 按钮！等待跳转到 subscription 页面...', 'success');

                    // 等待页面跳转
                    await new Promise(resolve => setTimeout(resolve, 3000));

                    // 检查是否跳转到了 subscription 页面
                    if (window.location.href.includes('account/subscription')) {
                        // 等待一下再刷新，确保页面完全加载
                        await new Promise(resolve => setTimeout(resolve, 2000));
                        window.location.reload();
                    }

                    return true;
                }
            }

            await new Promise(resolve => setTimeout(resolve, 500));
        }

        return false;
    }

    // 自动上传账单流程
    async function autoUploadInvoice() {
        try {
            // 1. 首先检查是否已经完成上传（Return to Home 按钮可见可点击）
            if (checkIfAlreadyCompleted()) {
                logger.log('检测到可点击的 Return to Home 按钮，账单已上传完成', 'success');
                await waitAndClickReturnHome();
                return;
            }

            // 2. 检查是否需要上传账单
            if (!checkIfNeedUpload()) {
                return;
            }

            // 3. 等待上传区域完全加载
            await new Promise(resolve => setTimeout(resolve, 2000));

            // 4. 再次检查是否有 Return to Home 按钮出现（可能在等待期间出现）
            if (checkIfAlreadyCompleted()) {
                await waitAndClickReturnHome();
                return;
            }

            // 5. 检查是否有保存的文件
            let fileToUpload = null;

            if (savedUploadFile) {
                if (savedUploadFile.isRestored) {
                    fileToUpload = await createFileFromSaved(savedUploadFile);
                } else {
                    fileToUpload = savedUploadFile;
                }
            }

            // 6. 如果没有保存的文件，创建模拟PDF账单
            if (!fileToUpload) {
                fileToUpload = createInvoicePDF();
            }

            // 7. 自动上传文件到拖拽区域
            logger.log('📤 正在上传账单文件...', 'info');
            const uploadSuccess = await autoUploadToDropZone(fileToUpload);

            if (!uploadSuccess) {
                logger.log('❌ 自动上传失败，请手动上传文件', 'error');
                return;
            }

            // 8. 等待 Upload Invoice 按钮可点击
            logger.log('⏳ 等待上传按钮可用...', 'info');
            const uploadButton = await waitForUploadInvoiceButton();

            if (uploadButton) {
                // 9. 点击 Upload Invoice 按钮
                logger.log('🎯 正在提交账单...', 'info');
                uploadButton.click();

                // 10. 等待并点击 Return to Home 按钮
                await waitAndClickReturnHome();
                logger.log('🎉 自动上传账单流程完成！', 'success');
            } else {
                logger.log('❌ 未找到 Upload Invoice 按钮，请手动点击', 'error');
            }

        } catch (error) {
            logger.log('自动上传账单失败: ' + error.message, 'error');
        }
    }

    // 自动上传文件到拖拽区域
    async function autoUploadToDropZone(file) {
        try {
            // 查找拖拽区域的多种可能选择器
            const dropZoneSelectors = [
                '.drag-drop-zone',
                '[data-testid="file-upload-zone"]',
                '.upload-area',
                '.dropzone',
                '[class*="drop"]',
                '[class*="upload"]',
                'input[type="file"]'
            ];

            let dropZone = null;
            for (const selector of dropZoneSelectors) {
                dropZone = document.querySelector(selector);
                if (dropZone) {
                    break;
                }
            }

            if (!dropZone) {
                return false;
            }

            // 创建拖拽事件的数据传输对象
            const dataTransfer = new DataTransfer();
            dataTransfer.items.add(file);

            // 如果是文件输入框
            if (dropZone.tagName === 'INPUT' && dropZone.type === 'file') {
                dropZone.files = dataTransfer.files;

                // 触发change事件
                dropZone.dispatchEvent(new Event('change', { bubbles: true }));
                dropZone.dispatchEvent(new Event('input', { bubbles: true }));

                return true;
            }

            // 1. 触发 dragenter 事件
            const dragenterEvent = new DragEvent('dragenter', {
                bubbles: true,
                cancelable: true,
                dataTransfer: dataTransfer
            });
            dropZone.dispatchEvent(dragenterEvent);

            // 2. 触发 dragover 事件
            const dragoverEvent = new DragEvent('dragover', {
                bubbles: true,
                cancelable: true,
                dataTransfer: dataTransfer
            });
            dropZone.dispatchEvent(dragoverEvent);

            // 3. 等待一小段时间
            await new Promise(resolve => setTimeout(resolve, 100));

            // 4. 触发 drop 事件
            const dropEvent = new DragEvent('drop', {
                bubbles: true,
                cancelable: true,
                dataTransfer: dataTransfer
            });
            dropZone.dispatchEvent(dropEvent);

            // 5. 等待一段时间让页面处理文件
            await new Promise(resolve => setTimeout(resolve, 1000));

            return true;

        } catch (error) {
            return false;
        }
    }

    // 创建模拟PDF账单
    function createInvoicePDF() {
        const currentDate = new Date().toISOString().split('T')[0];
        const invoiceNumber = 'INV-' + Date.now();

        // 创建更真实的PDF账单内容
        const pdfContent = `%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
>>
endobj

4 0 obj
<<
/Length 200
>>
stream
BT
/F1 12 Tf
50 750 Td
(INVOICE) Tj
0 -20 Td
(Invoice Number: ${invoiceNumber}) Tj
0 -20 Td
(Date: ${currentDate}) Tj
0 -20 Td
(Amount: $99.99) Tj
0 -20 Td
(Description: Cursor Pro Subscription) Tj
ET
endstream
endobj

xref
0 5
0000000000 65535 f
0000000009 00000 n
0000000058 00000 n
0000000115 00000 n
0000000206 00000 n
trailer
<<
/Size 5
/Root 1 0 R
>>
startxref
456
%%EOF`;

        const fileName = `cursor_invoice_${currentDate}.pdf`;
        return new File([pdfContent], fileName, { type: 'application/pdf' });
    }

    // 生成随机邮箱
    function generateEmail() {
        const firstName = FIRST_NAMES[Math.floor(Math.random() * FIRST_NAMES.length)];
        const lastName = LAST_NAMES[Math.floor(Math.random() * LAST_NAMES.length)];
        const timestamp = Date.now().toString(36); // 转换为36进制以缩短长度
        const randomNum = Math.floor(Math.random() * 10000).toString().padStart(4, '0'); // 生成4位随机数
        const username = `${firstName}${lastName}${timestamp}${randomNum}`;
        return `${username}${EMAIL_DOMAIN}`;
    }

    // 等待元素出现
    async function waitForElement(selector, timeout = 10000) {
        const startTime = Date.now();
        while (Date.now() - startTime < timeout) {
            const element = document.querySelector(selector);
            if (element) {
                return element;
            }
            await new Promise(resolve => setTimeout(resolve, 100));
        }
        return null;
    }

    // 从邮件文本中提取验证码
    function extractVerificationCode(mailText) {
        const codeMatch = mailText.match(/(?<![a-zA-Z@.])\b\d{6}\b/);
        return codeMatch ? codeMatch[0] : null;
    }

    // GoMail 获取验证码
    async function getGomailVerificationCode(email) {
        try {
            const emails = await getGomailEmails(email);
            if (emails.length === 0) {
                return null;
            }

            // 获取最新邮件的详情
            const latestEmail = emails[0];
            const emailDetail = await getGomailEmailDetail(latestEmail.id);

            const mailText = emailDetail.textContent || emailDetail.htmlContent || "";
            return extractVerificationCode(mailText);
        } catch (error) {
            logger.log("GoMail获取验证码失败: " + error.message, 'error');
            return null;
        }
    }

    // 获取验证码（带重试机制）
    async function getVerificationCode(email = null, maxRetries = 5, retryInterval = 3000) {
        for (let attempt = 0; attempt < maxRetries; attempt++) {
            logger.log(`尝试获取验证码 (第 ${attempt + 1}/${maxRetries} 次)...`);

            try {
                let code = null;

                if (currentEmailService === EMAIL_SERVICES.GOMAIL && email) {
                    code = await getGomailVerificationCode(email);
                } else {
                    logger.log("不支持的邮箱服务或缺少邮箱地址", 'error');
                    return null;
                }

                if (code) {
                    logger.log("成功获取验证码: " + code, 'success');
                    return code;
                }

                if (attempt < maxRetries - 1) {
                    logger.log(`未获取到验证码，${retryInterval/1000}秒后重试...`, 'warning');
                    await new Promise(resolve => setTimeout(resolve, retryInterval));
                }
            } catch (error) {
                logger.log("获取验证码出错: " + error, 'error');
                if (attempt < maxRetries - 1) {
                    await new Promise(resolve => setTimeout(resolve, retryInterval));
                }
            }
        }

        throw new Error(`经过 ${maxRetries} 次尝试后仍未获取到验证码。`);
    }

    // 自动填写邮箱并提交
    async function fillEmail() {
        let email;

        try {
            if (currentEmailService === EMAIL_SERVICES.GOMAIL) {
                logger.log('🔄 正在使用 GoMail 服务创建邮箱...', 'info');
                email = await createGomailMailbox();
                logger.log('✅ GoMail 邮箱创建成功: ' + email, 'success');
                // 存储邮箱地址供后续验证码获取使用
                GM_setValue('current_gomail_email', email);
            } else {
                email = generateEmail();
                logger.log('✅ 使用标准邮箱: ' + email, 'success');
            }
        } catch (error) {
            logger.log('❌ 创建邮箱失败: ' + error.message, 'error');
            return false;
        }

        const emailInput = await waitForElement('input[name="username"]');
        if (!emailInput) {
            logger.log('❌ 未找到邮箱输入框', 'error');
            return false;
        }

        logger.log('📝 正在填写邮箱地址...', 'info');
        // 填写邮箱
        emailInput.value = email;
        emailInput.dispatchEvent(new Event('input', { bubbles: true }));

        // 点击继续按钮
        const continueBtn = await waitForElement('button[type="submit"]');
        if (!continueBtn) {
            logger.log('❌ 未找到继续按钮', 'error');
            return false;
        }

        logger.log('✅ 邮箱填写完成，正在提交...', 'success');
        continueBtn.click();
        return true;
    }

    // 填写验证码
    async function fillVerificationCode() {
        logger.log('📧 正在获取验证码...', 'info');
        let email = null;

        // 获取当前使用的邮箱地址
        if (currentEmailService === EMAIL_SERVICES.GOMAIL) {
            email = GM_getValue('current_gomail_email', null);
            if (!email) {
                logger.log('❌ 未找到 GoMail 邮箱地址', 'error');
                return false;
            }
        }

        const code = await getVerificationCode(email);
        if (!code) {
            logger.log('❌ 未能获取验证码', 'error');
            return false;
        }

        logger.log('✅ 验证码获取成功: ' + code, 'success');

        const codeInput = await waitForElement('input[name="code"]');
        if (!codeInput) {
            logger.log('❌ 未找到验证码输入框', 'error');
            return false;
        }

        logger.log('📝 正在填写验证码...', 'info');
        // 填写验证码
        codeInput.value = code;
        codeInput.dispatchEvent(new Event('input', { bubbles: true }));

        // 点击继续按钮
        const continueBtn = await waitForElement('button[type="submit"]');
        if (!continueBtn) {
            logger.log('❌ 未找到继续按钮', 'error');
            return false;
        }

        logger.log('✅ 验证码填写完成，正在提交...', 'success');
        continueBtn.click();
        return true;
    }

    // 处理 Cloudflare Turnstile 验证
    async function handleCloudflareChallenge() {
        logger.log('🔍 正在查找 Cloudflare Turnstile 验证...', 'info');

        // 查找 Cloudflare iframe
        const iframe = document.querySelector('iframe[src*="challenges.cloudflare.com"]');
        if (!iframe) {
            logger.log('未找到 Cloudflare iframe', 'info');
            return false;
        }

        logger.log('✅ 找到 Cloudflare iframe，正在查找验证复选框...', 'info');

        // 从 iframe 开始向下查找 checkbox
        let checkbox = null;

        // 先检查 iframe 的父级容器中是否有 checkbox
        let parent = iframe.parentElement;
        while (parent && parent !== document.body) {
            checkbox = parent.querySelector('input[type="checkbox"]');
            if (checkbox) break;
            parent = parent.parentElement;
        }

        // 如果在父级容器中没找到，则在整个文档中查找
        if (!checkbox) {
            checkbox = document.querySelector('input[type="checkbox"]');
        }

        if (checkbox && !checkbox.checked) {
            logger.log('🎯 找到验证复选框，正在点击...', 'info');
            checkbox.click();
            logger.log('✅ Cloudflare 验证复选框已点击', 'success');
            return true;
        } else if (checkbox && checkbox.checked) {
            logger.log('✅ Cloudflare 验证复选框已经选中', 'success');
            return true;
        } else {
            logger.log('❌ 未找到 Cloudflare 验证复选框', 'error');
            return false;
        }
    }

    // 同意服务条款并完成注册
    async function completeRegistration() {
        logger.log('📋 正在处理服务条款...', 'info');

        // 首先尝试处理 Cloudflare 验证
        await handleCloudflareChallenge();

        const checkbox = await waitForElement('input[type="checkbox"]');
        if (checkbox && !checkbox.checked) {
            checkbox.click();
            logger.log('✅ 已自动勾选服务条款同意框', 'success');
        }

        const signupBtn = await waitForElement('button:contains("Sign up")');
        if (!signupBtn) {
            logger.log('❌ 未找到注册按钮', 'error');
            return false;
        }

        logger.log('🎯 正在完成注册...', 'info');
        signupBtn.click();
        return true;
    }

    // 处理 resources/cursor 页面
    async function handleResourcesCursorPage() {
        try {


            // 等待按钮出现，最多等待10秒
            const timeout = 10000;
            const startTime = Date.now();

            while (Date.now() - startTime < timeout) {
                // 多种方式查找 "Get your free month" 按钮
                let button = null;

                // 方式1: 通过 href 属性查找
                button = document.querySelector('a[href="https://app.augmentcode.com/promotions/cursor"]');

                // 方式2: 通过文本内容查找
                if (!button) {
                    const allLinks = Array.from(document.querySelectorAll('a'));
                    button = allLinks.find(link =>
                        link.textContent.includes('Get your free month') ||
                        link.textContent.includes('free month')
                    );
                }

                
                if (!button) {
                    button = document.querySelector('a.cursor-pointer.inline-flex.items-center.justify-center');
                }

                if (button && button.href && button.href.includes('promotions/cursor')) {
                    logger.log(`找到 "Get your free month" 按钮: "${button.textContent.trim()}"，自动点击...`, 'success');
                    button.click();
                    logger.log('✅ 已点击 "Get your free month" 按钮！', 'success');
                    return;
                }

                // 每500ms检查一次
                await new Promise(resolve => setTimeout(resolve, 500));
            }

            logger.log('未找到 "Get your free month" 按钮', 'warning');
        } catch (error) {
            logger.log('处理 resources/cursor 页面失败: ' + error.message, 'error');
        }
    }

    // 处理 account/subscription 页面
    async function handleSubscriptionPage() {
        try {
            // 检查是否刚刚完成了账单上传
            const justCompleted = GM_getValue('just_completed_upload', 0);
            const now = Date.now();
            const isRecentUpload = (now - justCompleted) < 60000; // 1分钟内

            if (isRecentUpload) {

                // 清除标记
                GM_setValue('just_completed_upload', 0);

                // 等待更长时间让余额更新
                await new Promise(resolve => setTimeout(resolve, 5000));
            } else {

                await new Promise(resolve => setTimeout(resolve, 3000));
            }

            // 多次尝试检测余额，因为可能需要时间更新
            const maxAttempts = isRecentUpload ? 6 : 3;
            let attempt = 0;

            while (attempt < maxAttempts) {
                attempt++;

                // 多种方式查找显示可用额度的元素
                let availableElements = [];

                // 方式1: 精确的类名选择器
                availableElements = Array.from(document.querySelectorAll('span.rt-Text.rt-r-size-5.rt-r-weight-medium'));

                // 方式2: 更宽泛的选择器
                if (availableElements.length === 0) {
                    availableElements = Array.from(document.querySelectorAll('span[class*="rt-Text"]'));
                }

                // 方式3: 查找所有包含 "available" 的元素
                if (availableElements.length === 0) {
                    availableElements = Array.from(document.querySelectorAll('*')).filter(el =>
                        el.textContent && el.textContent.includes('available')
                    );
                }



                let foundBalance = false;
                for (const element of availableElements) {
                    const text = element.textContent.trim();

                    if (text.includes('available')) {
                        foundBalance = true;

                        if (text === '125 available' || text.includes('125 available')) {
                            logger.log('✅ 检测到 125 available，跳转到 resources/cursor 页面...', 'success');
                            window.location.href = 'https://www.augmentcode.com/resources/cursor';
                            return;
                        } else if (text === '725 available' || text.includes('725 available')) {
                            logger.log('🎉 检测到 725 available，账单上传成功！等待10秒后自动退出登录...', 'success');

                            // 倒计时显示
                            for (let i = 10; i > 0; i--) {

                                await new Promise(resolve => setTimeout(resolve, 1000));
                            }

                            // 查找并点击 Logout 按钮
                            const logoutButton = document.querySelector('button[data-testid="logout-button"]') ||
                                               document.querySelector('button.base-header-logout-button') ||
                                               Array.from(document.querySelectorAll('button')).find(btn =>
                                                   btn.textContent.includes('Logout') || btn.textContent.includes('logout')
                                               );

                            if (logoutButton) {
                                logoutButton.click();
                                logger.log('✅ 已点击 Logout 按钮！账单上传流程完成！', 'success');
                            } else {
                                logger.log('❌ 未找到 Logout 按钮', 'warning');
                            }
                            return;
                        }
                    }
                }

                if (!foundBalance && attempt < maxAttempts) {
                    await new Promise(resolve => setTimeout(resolve, 3000));
                }
            }

        } catch (error) {
            logger.log('处理 subscription 页面失败: ' + error.message, 'error');
        }
    }

    // 主函数
    async function main() {
        // 检查是否是 resources/cursor 页面
        if (window.location.href.includes('www.augmentcode.com/resources/cursor')) {

            await handleResourcesCursorPage();
            return;
        }

        // 检查是否是 account/subscription 页面
        if (window.location.href.includes('app.augmentcode.com/account/subscription')) {


            // 检查是否需要刷新页面（刚从上传页面跳转过来）
            const justCompleted = GM_getValue('just_completed_upload', 0);
            const now = Date.now();
            const needsRefresh = (now - justCompleted) < 30000 && !GM_getValue('page_refreshed', false);

            if (needsRefresh) {

                GM_setValue('page_refreshed', true);
                // 延迟一下再刷新，确保页面完全加载
                setTimeout(() => {
                    window.location.reload();
                }, 2000);
                return;
            }

            // 清除刷新标记
            GM_setValue('page_refreshed', false);

            await handleSubscriptionPage();
            return;
        }

        // 检查是否是文件上传页面
        if (window.location.href.includes('/promotions/cursor')) {


            // 检查是否启用自动上传
            const autoUploadEnabled = GM_getValue('auto_upload_invoice', true);

            if (autoUploadEnabled) {


                // 等待上传区域加载
                const uploadElement = await waitForUploadElement();
                if (uploadElement) {
                    logger.log('找到文件上传区域，开始自动上传PDF账单...', 'success');

                    // 自动上传PDF账单
                    await autoUploadInvoice();
                } else {
                    logger.log('未找到文件上传区域', 'warning');
                }
            } else {


                // 仍然等待上传区域，以便手动操作
                const uploadElement = await waitForUploadElement();
                if (uploadElement) {
                    logger.log('找到文件上传区域，可使用设置面板手动上传', 'success');
                } else {
                    logger.log('未找到文件上传区域', 'warning');
                }
            }
            return;
        }

        // 只在注册相关页面运行
        if (!window.location.href.includes('login.augmentcode.com') &&
            !window.location.href.includes('auth.augmentcode.com')) {
            return;
        }

        logger.log('🚀 开始 AugmentCode 自动注册流程...', 'info');

        // 检查当前页面状态
        const emailInput = document.querySelector('input[name="username"]');
        const codeInput = document.querySelector('input[name="code"]');
        const termsCheckbox = document.querySelector('#terms-of-service-checkbox');
        const cloudflareIframe = document.querySelector('iframe[src*="challenges.cloudflare.com"]');

        if (emailInput) {
            logger.log('📧 检测到邮箱输入页面', 'info');
            // 显示注册按钮
            const registerButton = logger.showRegisterButton();
            if (registerButton) {
                registerButton.addEventListener('click', async () => {
                    try {
                        registerButton.disabled = true;
                        registerButton.textContent = '处理中...';
                        registerButton.style.backgroundColor = COLORS.warning;

                        logger.log('🎯 开始自动填写邮箱...', 'info');
                        await fillEmail();
                    } catch (error) {
                        logger.log('填写邮箱过程出错: ' + error, 'error');
                    } finally {
                        registerButton.disabled = false;
                        registerButton.textContent = '开始注册';
                        registerButton.style.backgroundColor = COLORS.secondary;
                    }
                });
            }
        } else if (codeInput) {
            logger.log('🔐 检测到验证码输入页面', 'info');
            try {
                if (await fillVerificationCode()) {
                    await new Promise(resolve => setTimeout(resolve, 2000));
                    await completeRegistration();
                    logger.log('🎉 ===== 注册流程完成！ =====', 'success');
                }
            } catch (error) {
                logger.log('❌ 填写验证码过程出错: ' + error, 'error');
            }
        } else if (termsCheckbox) {
            logger.log('📋 检测到服务条款页面', 'info');
            try {
                if (!termsCheckbox.checked) {
                    termsCheckbox.click();
                    logger.log('✅ 已自动勾选服务条款', 'success');
                }

                // 查找并点击注册按钮
                const signupBtn = await waitForElement('button[type="button"]');
                if (signupBtn) {
                    signupBtn.click();
                    logger.log('✅ 注册按钮点击完成', 'success');
                }
            } catch (error) {
                logger.log('❌ 勾选服务条款过程出错: ' + error, 'error');
            }
        } else if (cloudflareIframe) {
            logger.log('🛡️ 检测到 Cloudflare 验证页面', 'info');
            try {
                await handleCloudflareChallenge();
            } catch (error) {
                logger.log('❌ 处理 Cloudflare 验证过程出错: ' + error, 'error');
            }
        }
    }

    // 定期检查 Cloudflare 验证
    function startCloudflareMonitor() {
        const checkInterval = setInterval(async () => {
            const iframe = document.querySelector('iframe[src*="challenges.cloudflare.com"]');
            if (iframe) {
                logger.log('🛡️ 检测到 Cloudflare 验证，正在自动处理...', 'info');
                try {
                    const success = await handleCloudflareChallenge();
                    if (success) {
                        clearInterval(checkInterval);
                    }
                } catch (error) {
                    logger.log('❌ Cloudflare 验证处理失败: ' + error, 'error');
                }
            }
        }, 2000); // 每2秒检查一次

        // 30秒后停止监控
        setTimeout(() => {
            clearInterval(checkInterval);
        }, 30000);
    }

    // 启动脚本
    main().catch(error => logger.log('脚本执行出错: ' + error, 'error'));

    // 启动 Cloudflare 监控
    startCloudflareMonitor();
})();