# Augment Code 自动注册项目

这个项目提供了多种版本的 Augment Code 自动注册和账单上传脚本，支持完整的自动化流程。

## 📁 项目结构

```
augautoregist/
├── autoregist.js              # 🔧 油猴脚本版本（浏览器扩展）
├── autoregist_playwright.py   # 🐍 Python + Playwright 版本
├── ui_app.py                  # 🖥️ Streamlit 可视化界面
├── start_ui.py                # 🌐 UI 界面启动器
├── run.py                     # 🚀 Python 脚本启动器
├── start.bat                  # 💻 Windows 批处理启动器
├── test_browser.py            # 🧪 浏览器连接测试工具
├── config.json               # ⚙️ 配置文件
├── requirements.txt          # 📦 Python 依赖
├── debug_gomail.py           # 🔍 GoMail API 调试工具
├── main.py                   # 📧 邮箱内容调试脚本
└── README_playwright.md      # 📖 Playwright 版本详细说明
```

## 🎯 功能特性

### 🔄 完整自动化流程
- **自动注册**：邮箱创建 → 验证码获取 → 账户注册
- **智能导航**：Resources → Promotions → Subscription 页面自动跳转
- **文件上传**：自动生成并上传PDF账单文件
- **余额检测**：智能检测125/725余额并执行相应操作
- **自动退出**：完成流程后自动退出登录

### 📧 多邮箱服务支持
- **TempMail Plus**：免费临时邮箱服务
- **GoMail**：高级邮箱服务（需要Token）

### 🎮 多种使用方式
1. **🖥️ 可视化界面**：基于 Streamlit 的 Web UI，用户友好
2. **🔧 油猴脚本**：浏览器扩展，自动在页面上执行
3. **🐍 Python + Playwright**：独立程序，完全自动化
4. **⌨️ 命令行工具**：支持参数配置和批量操作

### 🆕 新增功能
- **🌐 代理支持**：支持 HTTP/SOCKS5 代理服务器
- **💾 自动保存**：配置更改时自动保存到文件
- **🧪 连接测试**：内置浏览器连接测试工具
- **📊 实时日志**：可视化界面实时显示执行日志
- **⚙️ 灵活配置**：支持多种配置方式和参数

## 🚀 快速开始

### 方式一：可视化界面（🌟 强烈推荐）

1. **安装依赖**
   ```bash
   # 使用 uv（推荐）
   uv add streamlit playwright aiohttp requests
   uv run playwright install chromium

   # 或使用 pip
   pip install -r requirements.txt
   playwright install chromium
   ```

2. **启动 UI 界面**
   ```bash
   # 使用 uv
   uv run start_ui.py

   # 或直接启动
   uv run streamlit run ui_app.py

   # 或使用批处理文件（Windows）
   start.bat  # 选择选项 1
   ```

3. **使用界面**
   - 浏览器会自动打开 `http://localhost:8501`
   - 在侧边栏配置各种参数
   - 点击"启动脚本"开始自动化
   - 实时查看执行日志

### 方式二：油猴脚本（适合浏览器用户）

1. **安装浏览器扩展**
   ```
   Chrome/Edge: 安装 Tampermonkey
   Firefox: 安装 Greasemonkey
   ```

2. **导入脚本**
   - 打开 Tampermonkey 管理面板
   - 点击"创建新脚本"
   - 复制 `autoregist.js` 内容并保存

3. **使用**
   - 访问 Augment Code 相关页面
   - 脚本会自动检测页面并执行相应操作

### 方式三：Python + Playwright（适合高级用户）

1. **安装依赖**
   ```bash
   # 使用 uv（推荐）
   uv add playwright aiohttp requests
   uv run playwright install chromium

   # 或使用 pip
   pip install -r requirements.txt
   playwright install chromium
   ```

2. **配置设置**
   ```json
   # 编辑 config.json 文件
   {
     "headless": false,
     "email_service": "tempmail",
     "start_url": "https://login.augmentcode.com",
     "proxy": {
       "enabled": false,
       "server": "http://proxy.example.com:8080",
       "username": "",
       "password": ""
     },
     "auto_save": true
   }
   ```

3. **运行脚本**
   ```bash
   # 基本运行
   uv run run.py

   # 使用 GoMail 服务
   uv run run.py --email-service gomail --gomail-token YOUR_TOKEN

   # 无头模式
   uv run run.py --headless

   # 调试模式
   uv run run.py --debug

   # 使用批处理文件（Windows）
   start.bat

   # 无头模式
   python run.py --headless

   # 使用 GoMail
   python run.py --email-service gomail --gomail-token your_token
   ```

### 方式三：Windows 一键启动

1. **双击运行**
   ```
   双击 start.bat 文件
   ```

2. **选择模式**
   - 正常模式（显示浏览器）
   - 无头模式（后台运行）
   - GoMail 模式
   - 自定义起始页面

## 📋 详细功能说明

### 🔧 油猴脚本功能
- ✅ 自动注册流程（邮箱输入 → 验证码 → 条款同意）
- ✅ Resources/Cursor 页面自动点击 "Get your free month"
- ✅ Promotions/Cursor 页面自动上传PDF账单
- ✅ Subscription 页面余额检测和自动退出
- ✅ 智能页面刷新和状态跟踪
- ✅ 详细日志输出和错误处理

### 🐍 Python + Playwright 功能
- ✅ 完整浏览器自动化（与油猴脚本功能一致）
- ✅ 多邮箱服务支持（TempMail + GoMail）
- ✅ 自动PDF文件生成和上传
- ✅ 智能页面检测和处理
- ✅ 配置文件支持
- ✅ 命令行参数支持
- ✅ 详细日志记录

### 🔍 调试工具
- **debug_gomail.py**：GoMail API 测试和邮件内容查看
- **main.py**：邮箱服务调试和验证码提取测试

## ⚙️ 配置说明

### config.json 配置文件
```json
{
  "headless": false,              // 是否无头模式
  "email_service": "tempmail",    // 邮箱服务类型
  "gomail_token": "gm_xxx",       // GoMail Token
  "start_url": "https://login.augmentcode.com",  // 起始页面
  "auto_upload_invoice": true,    // 自动上传账单
  "max_wait_time": 60            // 最大等待时间
}
```

### 邮箱服务配置

#### TempMail Plus（免费，推荐）
- 无需配置，开箱即用
- 稳定性较好，成功率高

#### GoMail（付费，高级）
- 需要购买 Token
- 更稳定，支持更多功能
- 在 config.json 中设置 token

## 🎮 使用场景

### 场景一：完整注册流程
```
启动脚本 → 注册页面 → 创建邮箱 → 输入邮箱 → 获取验证码
→ 输入验证码 → 同意条款 → 注册完成 → 自动跳转处理后续流程
```

### 场景二：账单上传流程
```
Resources页面 → 点击"Get your free month" → Promotions页面
→ 自动上传PDF账单 → 点击Upload → 点击Return to Home
→ Subscription页面 → 检测余额 → 自动退出登录
```

### 场景三：余额检测流程
```
Subscription页面 → 检测余额状态：
- 125 available → 跳转到 Resources 页面继续流程
- 725 available → 等待10秒后自动退出登录
```

## 🔧 高级功能

### 智能页面检测
- 自动识别当前页面类型
- 根据页面状态执行相应操作
- 支持页面跳转和状态变化

### 文件自动生成
- 自动创建符合要求的PDF账单文件
- 包含发票号码、日期、金额等信息
- 支持自定义账单内容

### 错误处理和重试
- 网络超时自动重试
- 元素查找失败重试
- 页面加载异常处理
- 详细错误日志记录

## 📊 日志和监控

### 日志输出
- 控制台实时输出执行状态
- 保存到 `autoregist.log` 文件
- 包含时间戳和详细操作信息

### 状态监控
- 实时显示当前执行步骤
- 错误和警告信息提示
- 成功操作确认信息

## 🛠️ 故障排除

### 常见问题

1. **浏览器启动失败**
   ```bash
   # 重新安装浏览器
   playwright install chromium
   ```

2. **依赖安装失败**
   ```bash
   # 升级 pip 并重新安装
   python -m pip install --upgrade pip
   pip install -r requirements.txt
   ```

3. **邮箱服务连接失败**
   - 检查网络连接
   - 验证 GoMail Token（如果使用）
   - 尝试切换邮箱服务

4. **页面元素找不到**
   - 页面可能已更新，需要调整选择器
   - 增加等待时间
   - 检查页面是否正确加载

5. **验证码获取失败**
   - 检查邮箱服务状态
   - 增加重试次数
   - 手动检查邮箱是否收到邮件

### 调试模式
- 设置 `headless: false` 查看浏览器操作
- 查看 `autoregist.log` 文件了解详细执行过程
- 使用调试工具测试邮箱服务

## 🔒 安全和合规

### 使用声明
- 本脚本仅用于自动化测试和学习目的
- 请遵守相关网站的使用条款和服务协议
- 不要用于恶意注册或滥用服务
- 使用者需自行承担使用风险

### 数据安全
- 脚本不会收集或上传个人信息
- 临时邮箱会在使用后自动失效
- 生成的PDF文件仅用于测试目的

## 📝 更新日志

### v2.0.0 (当前版本)
- ✅ 新增 Python + Playwright 版本
- ✅ 支持多邮箱服务（TempMail + GoMail）
- ✅ 添加配置文件支持
- ✅ 新增命令行参数支持
- ✅ 添加 Windows 批处理启动器
- ✅ 完善错误处理和重试机制
- ✅ 优化页面检测和状态跟踪

### v1.0.0
- ✅ 油猴脚本基础版本
- ✅ 基本注册和上传功能
- ✅ 页面自动检测
- ✅ 简单错误处理

## 🤝 贡献和支持

### 贡献代码
- 欢迎提交 Issue 报告问题
- 欢迎提交 Pull Request 改进功能
- 请遵循现有代码风格和注释规范

### 获取支持
- 查看详细文档：`README_playwright.md`
- 查看调试工具：`debug_gomail.py`
- 提交 Issue 获取帮助

### 项目维护
- 定期更新以适配网站变化
- 持续优化性能和稳定性
- 添加新功能和改进用户体验

---

**⚠️ 免责声明**：本项目仅供学习和测试使用，使用者需遵守相关法律法规和网站服务条款，作者不承担任何使用风险。